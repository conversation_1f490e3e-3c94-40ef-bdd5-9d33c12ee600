<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import deleteIcon from '@/assets/agent/clear.svg'
import documentIcon from '@/assets/agent/add.svg'
import microphoneIcon from '@/assets/agent/microphone.svg'
import { useSettingStore, useUserStore } from '@/store'
import TextComponent from '@/views/chat/components/Message/Text.vue'
import AvatarComponent from '@/views/chat/components/Message/Avatar.vue'
import { streamResponseManager } from '@/utils/streamResponseManager'
import { t } from '@/locales'

const props = defineProps<{
  modelConfig: {
    model: string
    temperature: number
    maxTokens: number
    topP: number
    frequencyPenalty: number
    presencePenalty: number
    systemMessage: string
    switch: boolean
    reasoningEffort: string
    modelId?: number // 添加可选的modelId属性
  }
  promptContent: string
}>()

const settingStore = useSettingStore()

// 测试消息相关
const testMessage = ref('')
// 使用Chat.Chat类型来保持与聊天页面一致的数据结构
const messages = ref<Chat.Chat[]>([])

// 生成唯一的会话ID用于测试
const testConversationId = 'preview-debug-test'

// 使用streamResponseManager管理loading状态
const loading = computed(() => streamResponseManager.isLoading(testConversationId))

// 滚动到底部
const scrollToBottom = () => {
  const chatMessages = document.querySelector('.chat-messages')
  if (chatMessages) {
    chatMessages.scrollTop = chatMessages.scrollHeight
  }
}

// 滚动到底部（如果已经在底部）
const scrollToBottomIfAtBottom = () => {
  const chatMessages = document.querySelector('.chat-messages')
  if (chatMessages) {
    const isAtBottom =
      chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10
    if (isAtBottom) {
      scrollToBottom()
    }
  }
}

// 自定义流式响应处理函数，专门用于测试环境
const processTestStreamResponse = async (response: Response, index: number) => {
  const reader = response.body?.getReader()
  const decoder = new TextDecoder()
  let accumulatedContent = ''
  let accumulatedReasoningContent = ''
  let accumulatedToolExecutionList: Chat.ToolExecution[] = []
  let sseBuffer = ''

  if (!reader) throw new Error('Failed to get response reader')

  try {
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value)
      sseBuffer += chunk

      // 简化的SSE解析
      const lines = sseBuffer.split('\n')
      sseBuffer = lines.pop() || '' // 保留最后一行（可能不完整）

      for (const line of lines) {
        if (line.startsWith('data:')) {
          const dataContent = line.substring(5).trim()
          if (!dataContent) continue

          try {
            const data = JSON.parse(dataContent)

            if (data.content !== undefined) {
              accumulatedContent += data.content
            }

            if (data.reasoningContent) {
              accumulatedReasoningContent += data.reasoningContent
            }

            if (data.toolExecution) {
              const newToolExecutionList = Array.isArray(data.toolExecution)
                ? data.toolExecution
                : [data.toolExecution]
              accumulatedToolExecutionList = [
                ...accumulatedToolExecutionList,
                ...newToolExecutionList,
              ]
            }

            // 更新AI回复消息
            if (messages.value[index]) {
              messages.value[index] = {
                ...messages.value[index],
                text: accumulatedContent,
                loading: !data.isEnd,
                error: false,
                reasoningContent: accumulatedReasoningContent || undefined,
                toolExecutionList:
                  accumulatedToolExecutionList.length > 0
                    ? accumulatedToolExecutionList
                    : undefined,
              }
            }

            if (data.isEnd) {
              break
            }

            scrollToBottomIfAtBottom()
          } catch (error) {
            console.error('解析响应数据失败:', error)
          }
        }
      }
    }
  } catch (error) {
    console.error('流式响应处理失败:', error)
    throw error
  }
}

// 处理测试消息发送
// resolved 用src\views\chat\hooks\useConversationActions.ts的streamResponse处理
const handleTestMessage = async () => {
  if (!testMessage.value.trim()) {
    return
  }
  if (loading.value) {
    return
  }

  const userMessage = testMessage.value
  testMessage.value = ''

  // 添加用户消息（使用Chat.Chat格式）
  const userChatData: Chat.Chat = {
    dateTime: new Date().toLocaleString(),
    text: userMessage,
    inversion: true,
    error: false,
    conversationOptions: {
      conversationId: testConversationId,
    },
  }
  messages.value.push(userChatData)

  // 添加AI回复占位消息
  const aiChatData: Chat.Chat = {
    dateTime: new Date().toLocaleString(),
    text: t('chat.thinking'),
    inversion: false,
    error: false,
    loading: true,
    conversationOptions: null,
  }
  messages.value.push(aiChatData)

  scrollToBottomIfAtBottom()

  let modelId = props.modelConfig.modelId
  if (!props.modelConfig.modelId) {
    const modelData = await settingStore.fetchDefaultModel(true)
    modelId = modelData.chatModelId
  }

  try {
    const requestData = {
      systemMessage: props.promptContent,
      userMessage,
      modelConfig: props.modelConfig,
      modelId,
      chatMessageList: messages.value.map(msg => ({
        role: msg.inversion ? 'user' : 'assistant',
        content: msg.text,
      })),
    }

    const token = useUserStore().token
    const response = await fetch(
      `${import.meta.env.VITE_APP_BASE_API}/biz/conversation/promptTest`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          Clientid: import.meta.env.VITE_APP_CLIENT_ID,
        },
        body: JSON.stringify(requestData),
        signal: streamResponseManager.getOrCreateState(testConversationId).controller.signal,
      },
    )

    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    // 使用自定义的流式响应处理函数
    await processTestStreamResponse(
      response,
      messages.value.length - 1, // AI回复消息的索引
    )
  } catch (error: any) {
    const lastIndex = messages.value.length - 1
    if (error.message === 'canceled') {
      messages.value[lastIndex] = {
        ...messages.value[lastIndex],
        text: '消息已取消发送',
        loading: false,
        error: false,
      }
    } else {
      messages.value[lastIndex] = {
        ...messages.value[lastIndex],
        text: `发生错误：${error.message}`,
        loading: false,
        error: true,
      }
    }
    scrollToBottom()
  }
}

// 清空对话
const handleClear = () => {
  messages.value = []
  // 清理streamResponseManager中的状态
  streamResponseManager.clearState(testConversationId)
}

// 处理按键事件
const handleKeyPress = (e: Event) => {
  const event = e as KeyboardEvent
  if (event.key === 'Enter' && !event.shiftKey) {
    e.preventDefault()
    handleTestMessage()
  }
}

// 生命周期钩子
onMounted(() => {
  // 组件挂载时清理可能存在的状态
  streamResponseManager.clearState(testConversationId)
})

onUnmounted(() => {
  // 组件卸载时清理状态
  streamResponseManager.clearState(testConversationId)
})
</script>

<template>
  <div class="preview-container">
    <div class="chat-bg">
      <div class="flex items-center justify-center">
        <img src="@/assets/agent/chat-bg.png" alt="chat-bg" />
        <div class="t">测试BOT</div>
      </div>
    </div>
    <div class="chat-messages">
      <div
        v-for="(message, index) in messages"
        :key="index"
        class="message"
        :class="{ user: message.inversion, assistant: !message.inversion }"
      >
        <div class="avatar">
          <!-- resolved 此处使用对应的用户头像和系统头像 -->
          <AvatarComponent :image="message.inversion" />
        </div>
        <div class="message-content">
          <TextComponent
            v-if="!message.inversion"
            :inversion="message.inversion"
            :error="message.error"
            :text="message.text"
            :loading="message.loading"
            :tool-execution-list="message.toolExecutionList"
            :chat-content-record-list="message.chatContentRecordList"
            :reasoning-content="message.reasoningContent"
          />
          <div v-else class="message-text">{{ message.text }}</div>
        </div>
      </div>
    </div>
    <div class="chat-input">
      <el-tooltip content="清空对话" placement="top">
        <div class="del-btn" @click="handleClear">
          <img :src="deleteIcon" alt="delete" />
        </div>
      </el-tooltip>
      <div class="input-box">
        <el-input
          v-model="testMessage"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 5 }"
          :placeholder="loading ? '正在生成回复...' : '请输入对话内容'"
          resize="none"
          @keydown.enter.prevent="handleKeyPress"
        >
          <!-- :disabled="loading" -->
        </el-input>
        <!-- <el-tooltip content="文件" placement="top">
          <div class="file-btn">
            <img :src="documentIcon" alt="document" />
          </div>
        </el-tooltip>
        <el-tooltip content="录音" placement="top">
          <div class="record-btn ml-2">
            <img :src="microphoneIcon" alt="microphone" />
          </div>
        </el-tooltip> -->
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .chat-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    pointer-events: none;
    z-index: 0;

    div {
      img {
        width: 64px;
        height: 64px;
        object-fit: contain;
      }

      .t {
        font-size: 20px;
        color: #4d5bec;
        line-height: 28px;
        text-align: left;
        font-style: bold;
        opacity: 0.3;
      }
    }
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e0e0e0;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background: #c0c0c0;
      }
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    // 当不滚动时隐藏滚动条
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: #e0e0e0;
      }
    }

    .message {
      position: relative;
      z-index: 1;
      display: flex;
      margin-bottom: 16px;
      // padding: 0 16px;

      &.user {
        .message-content {
          margin-left: 12px;
          max-width: 80%;

          .message-text {
            display: inline-block;
            background: #4865e8;
            color: #fff;
            border-radius: 4px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
      }

      &.assistant {
        .message-content {
          margin-left: 12px;
          max-width: 80%;
          .message-reply {
            display: inline-block;
            background: #f0f2f7; // 换一个美观的灰色底色
          }
        }
      }

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 12px;

        :deep(.n-avatar) {
          background-color: transparent;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .message-content {
        flex: 1;
      }
    }
  }

  .chat-input {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    // padding: 16px;
    .del-btn {
      width: 44px;
      height: 44px;
      background: #ffffff;
      border-radius: 24px;
      border: 1px solid #dadde8;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
      }
    }
    .input-box {
      flex: 1;
      margin-left: 12px;
      padding: 5px 8px;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      width: 332px;
      min-height: 44px;
      background: #ffffff;
      border-radius: 24px;
      border: 1px solid #dadde8;
      :deep(.el-textarea) {
        border: none !important;
        box-shadow: none !important;
        .el-textarea__inner {
          border: none !important;
          box-shadow: none !important;
        }
      }
      .file-btn,
      .record-btn {
        width: 32px;
        height: 32px;
        background: #ffffff;
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
