<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Delete, DocumentCopy, Edit, MoreFilled, Search } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import AgentBaseDialog from './my-agent/components/AgentBaseDialog.vue'
import { getBizAgentAgentStoreList } from '@/api/agent'
import { VERSION_STATUS_MAIN } from '@/utils/constants'

const route = useRoute()
const loading = ref(false)
const agentList = ref<Agent.AgentVo[]>([])
const queryFormRef = ref<FormInstance | null>(null)
const dialogVisible = ref(false)
const currentAgent = ref<Agent.AgentVo | null>(null)
const hasMore = ref(true)
const isLoading = ref(false) // 底部加载状态
const firstLoading = ref(true) // 首次加载状态

// 查询参数
const queryParams = ref<any>({
  pageNum: 1,
  pageSize: 48, // 增大每页数量
  categoryId: '',
  keyword: '',
})

// 加载助手列表
const getList = async () => {
  if (isLoading.value) return

  isLoading.value = true
  try {
    const res = await getBizAgentAgentStoreList<{ rows: Agent.AgentVo[] }>(queryParams.value)
    if (res.code === 200) {
      if (queryParams.value.pageNum === 1) {
        agentList.value = res.rows
      } else {
        agentList.value = [...agentList.value, ...res.rows]
      }

      // 判断是否还有更多数据
      hasMore.value = res.rows.length === queryParams.value.pageSize
    }
  } catch (error) {
    console.error('获取助手列表失败:', error)
  } finally {
    isLoading.value = false
    firstLoading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  console.log('loadMore called') // 添加日志确认是否触发
  if (isLoading.value || !hasMore.value) return
  queryParams.value.pageNum++
  getList()
}

// 监听路由参数变化
watch(
  () => route.query.categoryId,
  newCategory => {
    queryParams.value.categoryId = newCategory
    queryParams.value.pageNum = 1
    hasMore.value = true
    firstLoading.value = true
    getList()
  },
  { immediate: true },
)

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  handleQuery()
}

// 打开详情弹框
const handleCardClick = (agent: Agent.AgentVo) => {
  currentAgent.value = agent
  dialogVisible.value = true
}

// 开始对话
const handleStartChat = () => {
  // 这里添加开始对话的逻辑
  console.log('开始与', currentAgent.value?.name, '对话')
}

onMounted(() => {
  getList()
})
</script>

<template>
  <div class="agent-store-container">
    <div class="flex justify-between">
      <div class="flex">
        <!-- 预留位置，可以添加其他操作按钮 -->
      </div>
      <div class="flex justify-end">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
          <el-form-item label="" prop="keyword" class="!mr-0">
            <el-input
              v-model="queryParams.keyword"
              style="width: 400px"
              placeholder="请输入你需要搜索的内容"
              clearable
              @clear="handleQuery"
              @keyup.enter="handleQuery"
            >
              <template #suffix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery">高级搜索</el-button> -->
      </div>
    </div>

    <div
      v-infinite-scroll="loadMore"
      class="agent-list"
      :infinite-scroll-disabled="isLoading || !hasMore"
      :infinite-scroll-distance="30"
      :infinite-scroll-immediate="false"
      style="overflow: auto"
    >
      <!-- 首次加载时显示骨架屏 -->
      <div v-if="firstLoading" class="card-list">
        <div v-for="i in 8" :key="i" class="card">
          <el-skeleton animated>
            <template #template>
              <div class="card-header">
                <el-skeleton-item variant="circle" style="width: 50px; height: 50px" />
                <div class="info" style="width: 100%; margin-left: 12px">
                  <el-skeleton-item variant="text" style="width: 70%" />
                  <el-skeleton-item variant="text" style="width: 40%; margin-top: 8px" />
                </div>
              </div>
              <div class="card-content">
                <el-skeleton-item variant="p" style="width: 100%; height: 75px" />
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <!-- 数据加载后显示实际内容 -->
      <div v-else-if="agentList.length > 0" class="card-list">
        <div
          v-for="agent in agentList"
          :key="agent.id"
          class="card"
          @click="handleCardClick(agent)"
        >
          <div class="card-header">
            <div
              class="emoji-wrap !w-[56px] !h-[56px]"
              :style="{ backgroundColor: agent.backgroundColor }"
            >
              <img v-if="agent.emoji" :src="agent.emoji" class="emoji" />
            </div>
            <div class="info !h-[56px]">
              <div class="name ell">{{ agent.name }}</div>
              <div class="date flex items-center">
                <div class="max-w-[100px] ell">
                  {{ agent.createUserName }}
                </div>
                <div class="ml-1">|</div>
                <div class="max-w-[120px] ell ml-1">{{ agent.createTime }}</div>
              </div>
            </div>
          </div>
          <div class="card-content">
            {{ agent.description }}
          </div>
        </div>
      </div>

      <!-- 底部加载状态 -->
      <div v-if="isLoading && !firstLoading" class="loading-more">
        <el-icon class="loading-icon">
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" class="loading">
            <path
              fill="currentColor"
              d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"
            ></path>
          </svg>
        </el-icon>
        <span>加载中...</span>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!hasMore && !firstLoading && agentList.length > 0" class="no-more">
        <span>没有更多数据了</span>
      </div>

      <!-- 空数据提示 -->
      <div v-if="!firstLoading && agentList.length === 0" class="empty-data">
        <el-empty description="暂无数据" />
      </div>
    </div>

    <!-- 添加弹框组件 -->
    <AgentBaseDialog
      v-model="dialogVisible"
      :agent="currentAgent"
      :show-edit="false"
      @start-chat="handleStartChat"
    />
  </div>
</template>

<style scoped lang="less">
.agent-store-container {
  padding: 20px;
}

.agent-list {
  height: calc(100vh - 170px);
  overflow-y: auto;
  padding-right: 10px;
  padding-bottom: 30px;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  border: 2px solid transparent;
  &:hover {
    border: 2px solid rgba(77, 91, 236, 0.65);
  }

  .card-header {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .date {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .card-content {
    line-height: 1.5;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    // width: 303px;
    height: 75px;
    background: #f5f7fb;
    border-radius: 4px;
    color: #646a73;
    padding: 8px;
    font-size: 14px;
  }
}

.loading-more,
.no-more,
.empty-data {
  text-align: center;
  padding: 20px 0;
  color: #909399;
  font-size: 14px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  .loading-icon {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
