<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import type { FormRules } from 'element-plus'
import {
  CircleCheckFilled,
  CircleCloseFilled,
  DArrowRight,
  Delete,
  Edit,
  Lock,
  MoreFilled,
  Search,
  Sort,
  Timer,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import CategoryDialog from './components/CategoryDialog.vue'
import UpdateAgentCategoryDialog from './components/UpdateAgentCategoryDialog.vue'
import { SvgIcon } from '@/components/common'
import { VERSION_STATUS_MANAGE } from '@/utils/constants'
import {
  getBizAgentAdminAgentList,
  getBizAgentAdminAgentStatusCount,
  getBizAgentCategoryList,
  postBizAgentCategoryAddOrUpdate,
  postBizAgentCategoryRemoveId,
  postBizAgentRemoveAgentId,
  postBizAgentTurnToPrivateId,
} from '@/api/agent'

const router = useRouter()

// 当前选中的状态
const currentStatus = ref<number | string>('')

// 状态计数数据
const statusCounts = ref<{ status: number; count: number }[]>([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 48,
  keyword: '',
  categoryId: '',
  status: '' as string | number,
})

const queryFormRef = ref<any>(null)
const loading = ref(false)
const list = ref<Agent.AgentVo[]>([])
const hasMore = ref(true)
const isLoading = ref(false) // 底部加载状态
const firstLoading = ref(true) // 首次加载状态

/** 获取列表数据 */
const getList = async () => {
  if (isLoading.value) return

  isLoading.value = true
  try {
    console.log('请求参数:', queryParams.value)
    const res = await getBizAgentAdminAgentList<{ rows: Agent.AgentVo[] }>(queryParams.value)
    if (res.code === 200) {
      console.log('获取到的数据条数:', res.rows.length)
      console.log('获取到的数据:', res.rows)

      if (queryParams.value.pageNum === 1) {
        list.value = res.rows
      } else {
        list.value = [...list.value, ...res.rows]
      }

      // 判断是否还有更多数据
      hasMore.value = res.rows.length === queryParams.value.pageSize
    }
  } finally {
    isLoading.value = false
    firstLoading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  console.log('loadMore called') // 添加日志确认是否触发
  if (isLoading.value || !hasMore.value) return
  queryParams.value.pageNum++
  getList()
}

// 分类树数据
const categoryOptions = ref<Agent.AgentCategoryVo[]>([])
// 当前选中的分类ID
const currentCategoryId = ref<string>('')

// 加载分类数据
const loadCategoryData = async () => {
  try {
    const res = await getBizAgentCategoryList<Agent.AgentCategoryVo[]>()
    const { code, data, msg } = res
    if (code === 200) {
      // resolved 构造一层父级"全部分类"，点击"全部分类"能够达成查看全部助手的效果，并且对应高亮
      const storeRoot: Agent.AgentCategoryVo = {
        id: '',
        name: '全部分类',
        // agentNum: data?.reduce((sum, category) => sum + (category.agentNum || 0), 0) || 0,
        children: data || [],
      }
      categoryOptions.value = [storeRoot]
    } else {
      ElMessage.error(msg || '获取分类数据失败')
    }
  } catch (error) {
    console.error('获取分类数据出错:', error)
    ElMessage.error('获取分类数据出错')
  }
}

// 初始化分类选中状态
const initCategory = () => {
  const categoryId = router.currentRoute.value.query.categoryId as string
  if (categoryId) {
    currentCategoryId.value = categoryId
    queryParams.value.categoryId = categoryId
    // 找到对应的分类并设置为选中状态
    const category = categoryOptions.value.find(item => item.id === categoryId)
    if (category) {
      handleCategoryClick(category)
    }
  }
}

// 获取状态计数
const getStatusCounts = async (categoryId?: string) => {
  try {
    const res = await getBizAgentAdminAgentStatusCount<{ status: number; count: number }[]>({
      categoryId,
    })
    if (res.code === 200 && res.data) {
      statusCounts.value = res.data
    }
  } catch (error) {
    console.error('获取状态计数失败:', error)
  }
}

onMounted(() => {
  // 设置默认状态为全部
  currentStatus.value = ''
  getList()
  // 加载分类数据
  loadCategoryData().then(() => {
    // 数据加载完成后初始化分类选中状态
    initCategory()
  })
  // 获取状态计数
  getStatusCounts()
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  handleQuery()
}

const dialogVisible = ref(false)
const currentAgent = ref<Agent.AgentVo | null>(null)

// 添加修改分类对话框的状态变量
const updateCategoryDialogVisible = ref(false)
const currentUpdateAgent = ref<Agent.AgentVo | null>(null)

// 添加新的状态变量
const addCategoryDialogVisible = ref(false)
// 是否开启分类排序模式
const isCategorySortMode = ref(false)
// 排序前的分类数据备份
const categoriesBackup = ref<Agent.AgentCategoryVo[]>([])
// 排序后的分类列表
const sortedCategories = ref<Agent.AgentCategoryVo[]>([])

const handleCardClick = (item: Agent.AgentVo) => {
  currentAgent.value = item
  dialogVisible.value = true

  // 修改跳转路径
  router.push(`/settings/ai/agent-operation?id=${item.id}`)
}

const categoryTreeRef = ref(null)

// 编辑分类对话框
const editCategoryDialogVisible = ref(false)
const editingCategory = ref<Agent.AgentCategoryVo | null>(null)

// 编辑分类
const handleEditCategory = (data: Agent.AgentCategoryVo) => {
  editingCategory.value = data
  editCategoryDialogVisible.value = true
}

// 添加打开添加分类对话框的方法
const handleAddCategory = () => {
  addCategoryDialogVisible.value = true
}

// 开启分类排序模式
const handleEnableCategorySort = () => {
  isCategorySortMode.value = true
  ElMessage.info('已开启分类排序模式，可拖拽分类进行排序')

  // 备份当前分类数据
  if (categoryOptions.value.length > 0 && categoryOptions.value[0].children) {
    categoriesBackup.value = JSON.parse(JSON.stringify(categoryOptions.value[0].children))
    sortedCategories.value = JSON.parse(JSON.stringify(categoryOptions.value[0].children))
  }
}

// 取消分类排序
const handleCancelCategorySort = () => {
  isCategorySortMode.value = false
  ElMessage.info('已取消分类排序')

  // 恢复原始分类数据
  if (categoryOptions.value.length > 0 && categoriesBackup.value.length > 0) {
    const rootCategory = categoryOptions.value[0]
    rootCategory.children = categoriesBackup.value
  }

  loadCategoryData()
}

// 保存分类排序
const handleSaveCategorySort = async () => {
  try {
    // 构建排序后的分类ID列表
    const sortPromises = sortedCategories.value.map(async (category, index) => {
      // 使用postBizAgentCategoryAddOrUpdate接口更新每个分类的排序
      const updateData = {
        id: category.id,
        orderNum: index, // 使用索引作为排序值
        name: category.name, // 保留原有名称
      }

      const res = await postBizAgentCategoryAddOrUpdate<API.Response<any>>(updateData)
      return res.code === 200
    })

    // 等待所有排序更新完成
    const results = await Promise.all(sortPromises)

    // 检查是否所有排序都成功更新
    if (results.every(success => success)) {
      ElMessage.success('分类排序保存成功')
      isCategorySortMode.value = false
      // 重新加载分类数据
      await loadCategoryData()
    } else {
      ElMessage.error('保存分类排序失败，部分分类更新失败')
    }
  } catch (error) {
    console.error('保存分类排序失败:', error)
    ElMessage.error('保存分类排序失败')
  }
}

// 处理分类拖拽结束事件
const handleCategoryDragEnd = (draggingNode: any, dropNode: any, dropType: string, ev: any) => {
  // 只在排序模式下处理拖拽事件
  if (!isCategorySortMode.value) return

  // 更新排序后的分类列表
  if (categoryOptions.value.length > 0 && categoryOptions.value[0].children) {
    sortedCategories.value = JSON.parse(JSON.stringify(categoryOptions.value[0].children))
  }
}

// 控制拖拽时的放置规则，只允许同级排序
const allowDrop = (draggingNode: any, dropNode: any, type: string) => {
  // 只允许同级排序，不允许成为子节点
  return type !== 'inner' && draggingNode.parent === dropNode.parent
}

// 分类操作成功后的处理
const handleCategorySuccess = () => {
  // 重新加载分类数据
  loadCategoryData()
}

// 分类树节点点击
// resolved 点击后，id体现在url中，对应的，刷新后能根据url的id，获取到对应的数据
const handleCategoryClick = (data: Agent.AgentCategoryVo) => {
  currentCategoryId.value = data.id || ''
  // 更新URL，不刷新页面
  if (currentCategoryId.value) {
    router.push({
      query: {
        ...router.currentRoute.value.query,
        categoryId: data.id,
      },
    })
  } else {
    // 点击根节点（全部分类）时，清除URL中的categoryId
    const query = { ...router.currentRoute.value.query }
    delete query.categoryId
    router.push({ query })
  }

  // 更新查询参数并重新获取数据
  queryParams.value.categoryId = data.id || ''
  hasMore.value = true
  firstLoading.value = true
  queryParams.value.pageNum = 1
  handleQuery()
  console.log('选中的分类:', data)
  getStatusCounts(currentCategoryId.value)
}

// 状态筛选处理
const handleStatusFilter = (status: number | string) => {
  currentStatus.value = status
  queryParams.value.status = status
  queryParams.value.pageNum = 1
  hasMore.value = true
  firstLoading.value = true
  handleQuery()
}

const handleReview = (item: Agent.AgentVo) => {
  // 审核的逻辑
  console.log('审核', item)
  // 跳转到审核页面
  router.push(`/settings/ai/agent-operation?id=${item.id}`)
}

const handleSetPrivate = async (item: Agent.AgentVo) => {
  try {
    await ElMessageBox.confirm('确定要将该助手转为私有吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    if (!item.id) {
      ElMessage.error('助手ID不能为空')
      return
    }

    const res = await postBizAgentTurnToPrivateId<API.Response<any>>({ id: item.id })
    if (res.code === 200) {
      ElMessage.success('转为私有成功')
      // 只更新当前项的状态，不重新加载整个列表
      const index = list.value.findIndex(agent => agent.id === item.id)
      if (index !== -1) {
        list.value[index].status = -1 // 私有状态
      }
      // 更新分类数据，但不刷新列表
      loadCategoryData()
    } else {
      ElMessage.error(res.msg || '转为私有失败')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('转为私有失败:', error)
      ElMessage.error(error.message || '转为私有失败')
    }
  }
}

// resolved postBizAgentRemoveAgentId
const handleDeleteAgent = async (item: Agent.AgentVo) => {
  try {
    await ElMessageBox.confirm('确定要删除该助手吗？删除后将无法恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    if (!item.id) {
      ElMessage.error('助手ID不能为空')
      return
    }

    const res = await postBizAgentRemoveAgentId<API.Response<any>>({ id: item.id })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      // 直接从列表中移除被删除的项
      list.value = list.value.filter(agent => agent.id !== item.id)
      // 更新分类数据，但不刷新列表
      loadCategoryData()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('删除助手失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 删除分类
// 当分类下助手数量大于0时候，不可删除
const handleDeleteCategory = async (data: Agent.AgentCategoryVo) => {
  try {
    // 检查分类下是否有助手
    if (data.agentNum && data.agentNum > 0) {
      ElMessage.warning('该分类下存在助手，无法删除')
      return
    }

    await ElMessageBox.confirm('确定要删除该分类吗？删除后将无法恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    if (!data.id) {
      ElMessage.error('分类ID不能为空')
      return
    }

    const res = await postBizAgentCategoryRemoveId<API.Response<any>>({ id: data.id })
    if (res.code === 200) {
      ElMessage.success('删除成功')
      // 重新加载分类数据
      loadCategoryData()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('删除分类失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const currentHoverNodeId = ref<number | null>(null)

const handleDropdownVisibleChange = (visible: boolean, id: number) => {
  currentHoverNodeId.value = visible ? id : null
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'success'
    case 0:
      return 'pending'
    case 2:
      return 'failed'
    case -1:
      return 'private'
    default:
      return ''
  }
}

const handleUpdateCategory = (item: Agent.AgentVo) => {
  currentUpdateAgent.value = item
  updateCategoryDialogVisible.value = true
}

// 计算总数
const totalCount = computed(() => {
  return (
    statusCounts.value?.reduce(
      (total: number, current: { count: number }) => total + (current.count || 0),
      0,
    ) || 0
  )
})

// 获取指定状态的数量
const getStatusCount = (status: number) => {
  return (
    statusCounts.value?.find((item: { status: number; count: number }) => item.status === status)
      ?.count || 0
  )
}
</script>

<template>
  <div class="h-full">
    <el-row class="h-full">
      <!-- 分类树 -->
      <el-col :span="4" class="!p-[20px]">
        <div>
          <div class="flex justify-between items-center">
            <div class="category-tit">分类</div>
            <!-- resolved 点击添加分类弹出el对话框，标题为添加分类，内容为分类名称，确认后调用对应接口添加数据 -->
            <!-- resolved  展示el-dropdown，选项为添加分类、分类排序，点击分类排序，开启分类排序排序模式 -->
            <el-dropdown trigger="click">
              <SvgIcon
                class="text-lg cursor-pointer hover:text-primary el-dropdown-link !w-[18px] !h-[18px]"
                icon="icon-park:setting-config"
              />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleAddCategory">
                    <el-icon><Edit /></el-icon>
                    添加分类
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEnableCategorySort">
                    <el-icon><Sort /></el-icon>
                    分类排序
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <!-- resolved 通过接口加载分类 -->
          <!-- resolved 名称后面加上（助手数量） -->
          <!-- resolved 鼠标hover某一项时候，对应在右侧展示src\views\chat\layout\LayoutSider\List.vue里content-right-btn这样的编辑框和按钮 -->
          <div v-if="isCategorySortMode" class="sort-mode-controls mb-2">
            <el-alert
              title="排序模式已开启，请拖动分类进行排序"
              type="info"
              :closable="false"
              show-icon
            />
            <div class="flex justify-end mt-2">
              <el-button @click="handleCancelCategorySort">取消</el-button>
              <el-button type="primary" @click="handleSaveCategorySort">保存排序</el-button>
            </div>
          </div>
          <div class="my-tree mr-[-20px] pr-[20px] overflow-y-auto max-h-[calc(100vh-130px)]">
            <el-tree
              ref="categoryTreeRef"
              style="background: transparent"
              class="mt-2"
              node-key="id"
              :current-node-key="currentCategoryId"
              :data="categoryOptions"
              :props="{
                label: (data: Agent.AgentCategoryVo) => `${data.name}（${data.agentNum}）`,
                children: 'children',
              }"
              :expand-on-click-node="false"
              highlight-current
              default-expand-all
              :draggable="isCategorySortMode"
              :allow-drop="allowDrop"
              @node-drag-end="handleCategoryDragEnd"
              @node-click="handleCategoryClick"
            >
              <template #default="{ node, data }">
                <div class="tree-node-content">
                  <div class="flex items-center">
                    <el-icon v-if="isCategorySortMode && data.id" class="drag-icon mr-1"
                      ><Sort
                    /></el-icon>
                    <span
                      >{{ data.name }}
                      {{ data.agentNum !== undefined ? `（${data.agentNum}）` : '' }}</span
                    >
                  </div>
                  <!-- 只有非根节点才显示操作按钮 -->
                  <div
                    v-if="data.id && !data.isDefault"
                    class="tree-node-actions"
                    :class="{ show: currentHoverNodeId === data.id || node.isCurrent }"
                    @click.stop
                  >
                    <el-dropdown
                      trigger="click"
                      @visible-change="visible => handleDropdownVisibleChange(visible, data.id)"
                    >
                      <div class="content-right-btn el-dropdown-link">
                        <el-icon><MoreFilled /></el-icon>
                      </div>
                      <!-- resolved 点击删除分类弹出确认框，确认后调用对应接口删除并更新数据 -->
                      <!-- resolved 点击编辑分类弹出el对话框，标题为编辑分类，内容为分类名称，确认后调用对应接口更新数据 -->
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="handleEditCategory(data)">
                            <el-icon><Edit /></el-icon>
                            编辑分类
                          </el-dropdown-item>
                          <el-dropdown-item @click="handleDeleteCategory(data)">
                            <el-icon style="color: #f25b37"><Delete /></el-icon>
                            <span style="color: #f25b37">删除分类</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <!-- 主内容区 -->
      <el-col :span="20" class="border-l h-full !p-[20px]">
        <div class="flex justify-between">
          <div class="flex">
            <el-button
              :class="currentStatus === '' ? 'g-bg-active' : '!bg-[#fff]'"
              @click="handleStatusFilter('')"
            >
              全部 ({{ totalCount }})
            </el-button>
            <el-button
              v-for="status in VERSION_STATUS_MANAGE"
              :key="status.value"
              :class="currentStatus === status.value ? 'g-bg-active' : '!bg-[#fff]'"
              @click="handleStatusFilter(status.value)"
            >
              {{ status.name }} ({{ getStatusCount(status.value) }})
            </el-button>
          </div>
          <div class="flex justify-end">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
              <el-form-item label="" prop="keyword" class="!mr-0">
                <el-input
                  v-model="queryParams.keyword"
                  style="width: 400px"
                  placeholder="请输入你需要搜索的内容"
                  clearable
                  @clear="handleQuery"
                  @keyup.enter="handleQuery"
                >
                  <template #suffix>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
            <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery"> 高级搜索 </el-button> -->
          </div>
        </div>
        <!-- resolved 让agent-list区域在超出可视高度时候滚动overflow -->
        <div
          v-infinite-scroll="loadMore"
          class="agent-list"
          :infinite-scroll-disabled="isLoading || !hasMore"
          :infinite-scroll-distance="30"
          :infinite-scroll-immediate="false"
          style="overflow: auto"
        >
          <!-- 首次加载时显示骨架屏 -->
          <div v-if="firstLoading" class="card-list">
            <div v-for="i in 8" :key="i" class="card">
              <el-skeleton animated>
                <template #template>
                  <div class="card-header">
                    <el-skeleton-item variant="circle" style="width: 50px; height: 50px" />
                    <div class="info" style="width: 100%; margin-left: 12px">
                      <el-skeleton-item variant="text" style="width: 70%" />
                      <el-skeleton-item variant="text" style="width: 40%; margin-top: 8px" />
                    </div>
                  </div>
                  <div class="card-content">
                    <el-skeleton-item variant="p" style="width: 100%; height: 75px" />
                  </div>
                  <div class="card-footer">
                    <el-skeleton-item variant="text" style="width: 30%" />
                  </div>
                </template>
              </el-skeleton>
            </div>
          </div>

          <!-- 数据加载后显示实际内容 -->
          <div v-else-if="list.length > 0" class="card-list">
            <div
              v-for="(item, index) in list"
              :key="item.id"
              class="card"
              @click="handleCardClick(item)"
            >
              <!-- 添加索引显示，用于调试 -->
              <!-- <div class="absolute top-2 right-2 text-xs text-gray-400">{{ index + 1 }}</div> -->
              <div class="card-header">
                <div
                  class="emoji-wrap !w-[56px] !h-[56px]"
                  :style="{ backgroundColor: item.backgroundColor }"
                >
                  <img v-if="item.emoji" :src="item.emoji" class="emoji" />
                </div>
                <div class="info ml-2 !h-[56px]">
                  <div class="name ell">
                    {{ item.name }}
                  </div>
                  <div class="date flex items-center">
                    <div class="max-w-[100px] ell">
                      {{ item.createUserName }}
                    </div>
                    <div class="ml-1">|</div>
                    <div class="max-w-[120px] ell ml-1">{{ item.createTime }}</div>
                  </div>
                </div>
              </div>
              <div class="card-content">
                {{ item.description }}
              </div>
              <div class="card-footer">
                <div class="status" :class="getStatusClass(item.status)">
                  <el-icon v-if="item.status === 1">
                    <CircleCheckFilled />
                  </el-icon>
                  <el-icon v-else-if="item.status === 0">
                    <Timer />
                  </el-icon>
                  <el-icon v-else-if="item.status === 2">
                    <CircleCloseFilled />
                  </el-icon>
                  <el-icon v-else-if="item.status === -1">
                    <Lock />
                  </el-icon>
                  {{ VERSION_STATUS_MANAGE.find(s => s.value === item.status)?.name }}
                </div>
                <div class="actions" @click.stop>
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <el-icon><MoreFilled /></el-icon>
                    </span>
                    <!-- resolved 对应状态设置下拉按钮和click事件，并延伸逻辑
                        【私人助手】修改分类、删除
                        【待审核】审核、修改分类、删除
                        【审核失败】修改分类、删除
                        【已发布】转为私有、修改分类、删除
                        点卡片，默认弹出助手审核页（编辑页），可审核、修改分类、修改默认模型、调试，不可编辑提示词及调整参数项 -->
                    <template #dropdown>
                      <el-dropdown-menu>
                        <template v-if="item.status === -1">
                          <el-dropdown-item @click="handleUpdateCategory(item)">
                            <el-icon><Edit /></el-icon>
                            修改分类
                          </el-dropdown-item>
                        </template>

                        <template v-else-if="item.status === 0">
                          <el-dropdown-item @click="handleReview(item)">
                            <el-icon><Timer /></el-icon>
                            审核
                          </el-dropdown-item>
                          <el-dropdown-item @click="handleUpdateCategory(item)">
                            <el-icon><Edit /></el-icon>
                            修改分类
                          </el-dropdown-item>
                        </template>

                        <template v-else-if="item.status === 2">
                          <el-dropdown-item @click="handleUpdateCategory(item)">
                            <el-icon><Edit /></el-icon>
                            修改分类
                          </el-dropdown-item>
                        </template>

                        <template v-else-if="item.status === 1">
                          <!-- todo 调用接口postBizAgentTurnToPrivateId -->
                          <el-dropdown-item @click="handleSetPrivate(item)">
                            <el-icon><Lock /></el-icon>
                            转为私有
                          </el-dropdown-item>
                          <el-dropdown-item @click="handleUpdateCategory(item)">
                            <el-icon><Edit /></el-icon>
                            修改分类
                          </el-dropdown-item>
                        </template>

                        <el-dropdown-item @click="handleDeleteAgent(item)">
                          <el-icon style="color: #f25b37">
                            <Delete />
                          </el-icon>
                          <span style="color: #f25b37">删除</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部加载状态 -->
          <div v-if="isLoading && !firstLoading" class="loading-more">
            <el-icon class="loading-icon">
              <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" class="loading">
                <path
                  fill="currentColor"
                  d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"
                ></path>
              </svg>
            </el-icon>
            <span>加载中...</span>
          </div>

          <!-- 没有更多数据提示 -->
          <div v-if="!hasMore && !firstLoading && list.length > 0" class="no-more">
            <span>没有更多数据了</span>
          </div>

          <!-- 空数据提示 -->
          <div v-if="!firstLoading && list.length === 0" class="empty-data">
            <el-empty description="暂无数据" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <!-- 编辑分类对话框 -->
  <CategoryDialog
    v-model:visible="editCategoryDialogVisible"
    title="编辑分类"
    :category="editingCategory"
    @success="handleCategorySuccess"
  />
  <!-- 添加分类对话框 -->
  <CategoryDialog
    v-model:visible="addCategoryDialogVisible"
    title="添加分类"
    @success="handleCategorySuccess"
  />
  <!-- 修改分类对话框 -->
  <UpdateAgentCategoryDialog
    v-model:visible="updateCategoryDialogVisible"
    :agent="currentUpdateAgent"
    @success="
      () => {
        loadCategoryData()
        handleQuery()
      }
    "
  />
  <!-- resolved 补全修改分类handleUpdateCategory逻辑 -->
</template>

<style lang="less" scoped>
@import '@/styles/variables.less';

.agent-list {
  height: calc(100vh - 170px);
  overflow-y: auto;
  padding-right: 10px;
  padding-bottom: 30px;

  .card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;

    .card {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      border: 2px solid transparent;
      &:hover {
        border: 2px solid rgba(77, 91, 236, 0.65);
      }

      .card-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .emoji {
          width: 24px;
          height: 24px;
        }

        .info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .date {
            font-size: 12px;
            color: #999;
          }
        }

        .actions {
          .el-dropdown-link {
            cursor: pointer;
            color: #999;
          }
        }
      }

      .card-content {
        line-height: 1.5;
        margin-bottom: 12px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        // width: 303px;
        height: 75px;
        background: #f5f7fb;
        border-radius: 4px;
        color: #646a73;
        padding: 8px;
        font-size: 14px;
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .status {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;

          &.success {
            color: #67c23a;
            background: rgba(103, 194, 58, 0.1);
          }

          &.pending {
            color: #e6a23c;
            background: rgba(230, 162, 60, 0.1);
          }

          &.failed {
            color: #f56c6c;
            background: rgba(245, 108, 108, 0.1);
          }

          &.private {
            // resolved color为主色，能使用src\styles\global.less的@c吗
            color: @c;
            background: fade(@c, 10%);
          }
        }
      }
    }
  }

  .loading-more,
  .no-more,
  .empty-data {
    text-align: center;
    padding: 20px 0;
    color: #909399;
    font-size: 14px;
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .loading-icon {
      animation: rotating 2s linear infinite;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.el-dropdown-link {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: rgba(230, 234, 244, 0.6);
    border-radius: 4px;
  }
}

.my-card {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  :deep(.el-card__header) {
    padding: 0 !important;
  }
  :deep(.el-card__body) {
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
}

.category-tit {
  width: 32px;
  height: 22px;
  font-size: 14px;
  color: #646a73;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.tree-node-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;

  .drag-icon {
    color: #909399;
    cursor: move;
    &:hover {
      color: @c;
    }
  }

  .tree-node-actions {
    display: none;
    &.show {
      display: block;
    }
  }

  &:hover {
    .tree-node-actions {
      display: block;
    }
  }
}

.content-right-btn {
  height: 32px;
  width: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  cursor: pointer;
}
</style>
